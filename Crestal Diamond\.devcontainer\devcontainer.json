// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
	"name": "Python 3",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"image": "mcr.microsoft.com/devcontainers/python:1-3.12-bullseye",
	"features": {
		"ghcr.io/devcontainers/features/git-lfs:1": {
			"autoPull": true,
			"installDirectlyFromGitHubRelease": true,
			"version": "latest"
		},
		"ghcr.io/devcontainers/features/powershell:1": {
			"version": "latest"
		},
		"ghcr.io/dotnet/aspire-devcontainer-feature/dotnetaspire:1": {
			"version": "latest daily"
		}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"undefined_publisher.smart-collaborative-agent-ai"
			]
		}
	}

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "pip3 install --user -r requirements.txt",

	// Configure tool-specific properties.
	// "customizations": {},

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
